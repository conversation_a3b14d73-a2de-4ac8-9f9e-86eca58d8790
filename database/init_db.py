import os
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from database.models import Base, User, Role, Server, Service, ServicePermission
from config.settings import settings

def create_database():
    """Create database and tables"""
    # Ensure database directory exists
    db_dir = os.path.dirname(settings.DATABASE_URL.replace('sqlite:///', ''))
    if db_dir and not os.path.exists(db_dir):
        os.makedirs(db_dir)
    
    engine = create_engine(settings.DATABASE_URL)
    Base.metadata.create_all(engine)
    return engine

def init_default_data(engine):
    """Initialize database with default data"""
    Session = sessionmaker(bind=engine)
    session = Session()
    
    try:
        # Create default roles
        admin_role = session.query(Role).filter_by(name='Admin').first()
        if not admin_role:
            admin_role = Role(name='Admin', description='Full access to all services')
            session.add(admin_role)
        
        operator_role = session.query(Role).filter_by(name='Operator').first()
        if not operator_role:
            operator_role = Role(name='Operator', description='Can start/stop/restart services')
            session.add(operator_role)
        
        viewer_role = session.query(Role).filter_by(name='Viewer').first()
        if not viewer_role:
            viewer_role = Role(name='Viewer', description='Can only view service status and logs')
            session.add(viewer_role)
        
        session.commit()
        
        # Create default admin user
        admin_user = session.query(User).filter_by(username=settings.DEFAULT_ADMIN_USERNAME).first()
        if not admin_user:
            admin_user = User(
                username=settings.DEFAULT_ADMIN_USERNAME,
                email=settings.DEFAULT_ADMIN_EMAIL
            )
            admin_user.set_password(settings.DEFAULT_ADMIN_PASSWORD)
            admin_user.roles.append(admin_role)
            session.add(admin_user)
        
        # Create sample server (localhost)
        localhost_server = session.query(Server).filter_by(name='localhost').first()
        if not localhost_server:
            localhost_server = Server(
                name='localhost',
                hostname='localhost',
                port=22,
                username='root',
                key_path=settings.SSH_KEY_PATH
            )
            session.add(localhost_server)
        
        session.commit()
        
        # Create sample services
        sample_services = [
            {
                'name': 'nginx',
                'service_command': 'nginx',
                'log_path': '/var/log/nginx/access.log',
                'description': 'Nginx web server'
            },
            {
                'name': 'apache2',
                'service_command': 'apache2',
                'log_path': '/var/log/apache2/access.log',
                'description': 'Apache web server'
            },
            {
                'name': 'mysql',
                'service_command': 'mysql',
                'log_path': '/var/log/mysql/error.log',
                'description': 'MySQL database server'
            }
        ]
        
        for service_data in sample_services:
            existing_service = session.query(Service).filter_by(
                name=service_data['name'], 
                server_id=localhost_server.id
            ).first()
            if not existing_service:
                service = Service(
                    name=service_data['name'],
                    server_id=localhost_server.id,
                    service_command=service_data['service_command'],
                    log_path=service_data['log_path'],
                    description=service_data['description']
                )
                session.add(service)
        
        session.commit()
        
        print("Database initialized successfully!")
        print(f"Default admin user: {settings.DEFAULT_ADMIN_USERNAME}")
        print(f"Default admin password: {settings.DEFAULT_ADMIN_PASSWORD}")
        
    except Exception as e:
        session.rollback()
        print(f"Error initializing database: {e}")
        raise
    finally:
        session.close()

def get_db_session():
    """Get database session"""
    engine = create_engine(settings.DATABASE_URL)
    Session = sessionmaker(bind=engine)
    return Session()

if __name__ == "__main__":
    engine = create_database()
    init_default_data(engine)
