# Service Management Portal

A centralized web-based service management portal built with Streamlit that allows you to manage services across multiple servers from a single interface.

## Features

### 🔐 Authentication & Authorization
- Secure login system with session management
- Role-based access control (RBAC)
- Three default roles: Admin, Operator, Viewer
- Granular permissions per service

### 🖥️ Service Management
- View service status across multiple servers
- Start, stop, and restart services
- Real-time service status monitoring
- Support for systemd services

### 📋 Logging & Monitoring
- Real-time log viewing with configurable line counts
- Support for both log files and journalctl
- Log search functionality
- Auto-refresh capabilities

### 🌐 Multi-server Support
- Manage services on multiple remote servers
- SSH key-based authentication
- Connection testing and validation
- Localhost support for local services

### 📊 Audit & Reporting
- Complete audit trail of all service operations
- User activity tracking
- Filterable audit logs
- Export capabilities

## Quick Start

### Prerequisites
- Python 3.8 or higher
- SSH access to target servers (for remote management)
- systemd-based services on target servers

### Installation

1. **Clone or download the project:**
```bash
git clone <repository-url>
cd service_management_portal
```

2. **Install dependencies:**
```bash
pip install -r requirements.txt
```

3. **Initialize the database:**
```bash
python database/init_db.py
```

4. **Run the application:**
```bash
streamlit run app.py
```

5. **Access the portal:**
Open your browser and navigate to `http://localhost:8501`

### Default Login
- **Username:** `admin`
- **Password:** `admin123`

⚠️ **Important:** Change the default password after first login!

## Configuration

### Environment Variables
Create a `.env` file in the project root:

```env
# Database
DATABASE_URL=sqlite:///database/service_portal.db

# Security
SECRET_KEY=your-secret-key-here
SESSION_TIMEOUT=3600

# SSH Settings
SSH_TIMEOUT=30
SSH_KEY_PATH=~/.ssh/id_rsa

# Application
DEBUG=False
LOG_LEVEL=INFO
MAX_LOG_LINES=1000

# Default Admin (for initial setup)
DEFAULT_ADMIN_USERNAME=admin
DEFAULT_ADMIN_PASSWORD=admin123
DEFAULT_ADMIN_EMAIL=<EMAIL>
```

### SSH Setup
For remote server management, ensure:

1. SSH key-based authentication is configured
2. The user has sudo privileges on target servers
3. SSH keys are accessible from the portal server

## Project Structure

```
service_management_portal/
├── app.py                      # Main Streamlit application
├── requirements.txt            # Python dependencies
├── config/
│   ├── settings.py            # Application configuration
│   └── database.py            # Database configuration
├── auth/
│   ├── authentication.py      # Login/logout functionality
│   └── authorization.py       # Role-based access control
├── services/
│   ├── service_manager.py     # Service operations
│   ├── server_connector.py    # SSH/API communication
│   └── log_viewer.py         # Real-time log viewing
├── database/
│   ├── models.py             # SQLAlchemy models
│   ├── init_db.py            # Database initialization
│   └── service_portal.db     # SQLite database
├── ui/
│   ├── dashboard.py          # Main dashboard interface
│   ├── login.py              # Login page
│   └── components.py         # Reusable UI components
└── utils/
    ├── helpers.py            # Utility functions
    └── validators.py         # Input validation
```

## Database Schema

### Core Tables
- **users**: User accounts and credentials
- **roles**: User roles (Admin, Operator, Viewer)
- **user_roles**: Many-to-many relationship between users and roles
- **servers**: Server configurations and connection details
- **services**: Service definitions and configurations
- **service_permissions**: Granular permissions per role/service
- **audit_logs**: Complete audit trail of all operations

## Usage Guide

### Adding Servers
1. Login as Admin
2. Navigate to Settings
3. Add server configuration with SSH details
4. Test connection

### Adding Services
1. Ensure server is configured
2. Add service with systemd service name
3. Configure log file path (optional)
4. Set permissions for roles

### Managing Services
1. View service status on dashboard
2. Use control buttons to start/stop/restart
3. View logs in real-time
4. Monitor service health

### User Management
1. Admin can create new users
2. Assign roles to users
3. Configure service permissions per role
4. Monitor user activity via audit logs

## Security Considerations

### Authentication
- Passwords are hashed using bcrypt
- Session-based authentication with timeout
- Input validation and sanitization

### Authorization
- Role-based access control
- Granular permissions per service
- Audit logging for all operations

### Network Security
- SSH key-based authentication for servers
- No password storage for server access
- Connection timeout and error handling

### Deployment Security
- Change default credentials
- Use strong secret keys
- Configure proper SSH key permissions
- Regular security updates

## Troubleshooting

### Common Issues

**Database Connection Error:**
- Ensure database directory exists
- Check file permissions
- Verify SQLite installation

**SSH Connection Failed:**
- Verify SSH key path and permissions
- Check server hostname and port
- Ensure user has sudo privileges

**Service Not Found:**
- Verify systemd service name
- Check service exists on target server
- Confirm user permissions

**Permission Denied:**
- Check user role assignments
- Verify service permissions
- Ensure proper SSH key setup

### Logs and Debugging
- Enable debug mode in settings
- Check application logs
- Monitor SSH connection status
- Review audit logs for user actions

## Development

### Adding New Features
1. Follow the existing project structure
2. Add appropriate tests
3. Update documentation
4. Follow security best practices

### Database Migrations
- Modify models in `database/models.py`
- Update initialization in `database/init_db.py`
- Test with fresh database

### Custom Authentication
- Extend `auth/authentication.py`
- Implement custom providers
- Update UI components accordingly

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support and questions:
1. Check the troubleshooting section
2. Review the documentation
3. Check existing issues
4. Create a new issue with detailed information

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

---

**Note:** This is a powerful tool that can control critical services. Always test in a development environment before deploying to production.
