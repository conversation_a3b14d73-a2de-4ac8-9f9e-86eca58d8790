from datetime import datetime
from database.init_db import get_db_session
from database.models import Service, Server, AuditLog
from services.server_connector import ServerConnector
from auth.authentication import auth_manager

class ServiceManager:
    def __init__(self):
        pass
    
    def get_service_status(self, service_id):
        """Get current status of a service"""
        session = get_db_session()
        try:
            service = session.query(Service).filter_by(id=service_id).first()
            if not service:
                return False, "Service not found", None
            
            server = service.server
            connector = ServerConnector(
                hostname=server.hostname,
                port=server.port,
                username=server.username,
                key_path=server.key_path
            )
            
            success, status = connector.get_service_status(service.service_command)
            connector.disconnect()
            
            return success, status, service
        
        except Exception as e:
            return False, f"Error getting service status: {str(e)}", None
        finally:
            session.close()
    
    def start_service(self, service_id):
        """Start a service"""
        return self._perform_service_action(service_id, 'start')
    
    def stop_service(self, service_id):
        """Stop a service"""
        return self._perform_service_action(service_id, 'stop')
    
    def restart_service(self, service_id):
        """Restart a service"""
        return self._perform_service_action(service_id, 'restart')
    
    def _perform_service_action(self, service_id, action):
        """Perform service action (start/stop/restart)"""
        session = get_db_session()
        try:
            service = session.query(Service).filter_by(id=service_id).first()
            if not service:
                return False, "Service not found"
            
            server = service.server
            connector = ServerConnector(
                hostname=server.hostname,
                port=server.port,
                username=server.username,
                key_path=server.key_path
            )
            
            # Perform the action
            if action == 'start':
                success, message = connector.start_service(service.service_command)
            elif action == 'stop':
                success, message = connector.stop_service(service.service_command)
            elif action == 'restart':
                success, message = connector.restart_service(service.service_command)
            else:
                return False, f"Unknown action: {action}"
            
            connector.disconnect()
            
            # Log the action
            self._log_action(session, service_id, action, 'success' if success else 'failed', message)
            
            return success, message
        
        except Exception as e:
            error_msg = f"Error performing {action}: {str(e)}"
            self._log_action(session, service_id, action, 'failed', error_msg)
            return False, error_msg
        finally:
            session.close()
    
    def get_service_logs(self, service_id, lines=100, use_log_file=True):
        """Get service logs"""
        session = get_db_session()
        try:
            service = session.query(Service).filter_by(id=service_id).first()
            if not service:
                return False, "Service not found"
            
            server = service.server
            connector = ServerConnector(
                hostname=server.hostname,
                port=server.port,
                username=server.username,
                key_path=server.key_path
            )
            
            # Try to get logs from log file first, then fallback to journalctl
            if use_log_file and service.log_path:
                success, logs = connector.get_log_file_content(service.log_path, lines)
                if not success:
                    # Fallback to journalctl
                    success, logs = connector.get_service_logs(service.service_command, lines)
            else:
                success, logs = connector.get_service_logs(service.service_command, lines)
            
            connector.disconnect()
            
            # Log the view action
            self._log_action(session, service_id, 'view_logs', 'success' if success else 'failed', 
                           f"Viewed {lines} lines of logs")
            
            return success, logs
        
        except Exception as e:
            error_msg = f"Error getting logs: {str(e)}"
            self._log_action(session, service_id, 'view_logs', 'failed', error_msg)
            return False, error_msg
        finally:
            session.close()
    
    def get_all_services_status(self):
        """Get status of all accessible services"""
        from auth.authorization import auth_manager_auth
        
        services = auth_manager_auth.get_accessible_services()
        services_status = []
        
        for service in services:
            success, status, _ = self.get_service_status(service.id)
            services_status.append({
                'id': service.id,
                'name': service.name,
                'server': service.server.name,
                'description': service.description,
                'status': status if success else 'unknown',
                'permissions': auth_manager_auth.get_service_permissions(service.id)
            })
        
        return services_status
    
    def test_server_connection(self, server_id):
        """Test connection to a server"""
        session = get_db_session()
        try:
            server = session.query(Server).filter_by(id=server_id).first()
            if not server:
                return False, "Server not found"
            
            connector = ServerConnector(
                hostname=server.hostname,
                port=server.port,
                username=server.username,
                key_path=server.key_path
            )
            
            success, message = connector.test_connection()
            return success, message
        
        except Exception as e:
            return False, f"Connection test error: {str(e)}"
        finally:
            session.close()
    
    def _log_action(self, session, service_id, action, result, details=None):
        """Log service action to audit log"""
        try:
            user = auth_manager.get_current_user()
            if user:
                audit_log = AuditLog(
                    user_id=user.id,
                    service_id=service_id,
                    action=action,
                    result=result,
                    details=details
                )
                session.add(audit_log)
                session.commit()
        except Exception as e:
            print(f"Error logging action: {e}")

# Global service manager instance
service_manager = ServiceManager()
